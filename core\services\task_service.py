
from adapters.email_service.email_sender import <PERSON>ail<PERSON><PERSON>
from adapters.email_service.templates import generate_task_template

def perform_business_logic_and_notify(user_email: str, task_name: str):
    print(f"Processing task: {task_name} for user: {user_email}")

    subject = f"System Task Notification: {task_name}"
    html_body = generate_task_template(task_name)

    EmailSender().send_email(to_address=user_email, subject=subject, html_body=html_body)
