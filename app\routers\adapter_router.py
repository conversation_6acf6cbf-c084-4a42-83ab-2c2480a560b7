
from fastapi import APIRouter, Body
from adapters.email_service.email_sender import <PERSON><PERSON><PERSON><PERSON>
from adapters.email_service.templates import generate_task_template

router = APIRouter()

@router.post("/send-mail")
def send_adapter_email(to_email: str = Body(...), task_name: str = Body(...)):
    subject = f"Adapter Email: {task_name}"
    html_body = generate_task_template(task_name)

    EmailSender().send_email(to_address=to_email, subject=subject, html_body=html_body)
    return {"status": "Email sent directly from adapter"}
