
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os

EMAIL_HOST = os.getenv("EMAIL_HOST", "smtp.gmail.com")
EMAIL_PORT = int(os.getenv("EMAIL_PORT", "587"))
EMAIL_USER = os.getenv("EMAIL_USER")
EMAIL_PASS = os.getenv("EMAIL_PASS")

# Validate required environment variables
if not EMAIL_USER:
    raise ValueError("EMAIL_USER environment variable is required")
if not EMAIL_PASS:
    raise ValueError("EMAIL_PASS environment variable is required")

class EmailSender:
    def send_email(self, to_address, subject, html_body):
        """
        Send an email with HTML content.

        Args:
            to_address (str): Recipient email address
            subject (str): Email subject
            html_body (str): HTML content of the email

        Raises:
            ValueError: If required parameters are missing
            smtplib.SMTPException: If email sending fails
        """
        if not to_address:
            raise ValueError("to_address is required")
        if not subject:
            raise ValueError("subject is required")
        if not html_body:
            raise ValueError("html_body is required")

        msg = MIMEMultipart()
        msg['From'] = EMAIL_USER
        msg['To'] = to_address
        msg['Subject'] = subject
        msg.attach(MIMEText(html_body, 'html'))

        try:
            with smtplib.SMTP(EMAIL_HOST, EMAIL_PORT) as server:
                server.starttls()
                server.login(EMAIL_USER, EMAIL_PASS)
                server.send_message(msg)
        except smtplib.SMTPException as e:
            raise smtplib.SMTPException(f"Failed to send email: {str(e)}")
