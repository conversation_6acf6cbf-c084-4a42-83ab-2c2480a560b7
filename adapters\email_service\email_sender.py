# adapters/email_service/email_sender.py

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from .config import EMAIL_HOST, EMAIL_PORT, EMAIL_USER, EMAIL_PASS

class EmailSender:
    def __init__(self):
        self.server = smtplib.SMTP(EMAIL_HOST, EMAIL_PORT)
        self.server.starttls()
        self.server.login(EMAIL_USER, EMAIL_PASS)

    def send_email(self, to_address, subject, message_body):
        msg = MIMEMultipart()
        msg['From'] = EMAIL_USER
        msg['To'] = to_address
        msg['Subject'] = subject

        msg.attach(MIMEText(message_body, 'plain'))

        self.server.send_message(msg)

    def quit(self):
        self.server.quit()
